import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import seaborn as sns
import glob
import os
import warnings
warnings.filterwarnings("ignore")

from tensorflow import keras
import tensorflow as tf
from tensorflow.keras import Model, Input
from tensorflow.keras.layers import <PERSON><PERSON>, Flatten
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.callbacks import ModelCheckpoint, ReduceLROnPlateau, EarlyStopping
from tensorflow.keras.applications import Xception

# Load CSV and Image Paths
train = pd.read_csv("insat_3d_ds - Sheet.csv")

# --- Data checks ---
img_dir = "DATASET/insat3d_ir_cyclone_ds/CYCLONE_DATASET_INFRARED"
missing = []
for fname in train['img_name']:
    if not os.path.exists(os.path.join(img_dir, fname)):
        missing.append(fname)
if missing:
    print("Missing files:", missing)

train['label'] = pd.to_numeric(train['label'], errors='coerce')
if train['label'].isnull().any():
    print("Warning: Some labels are not numeric or missing.")

# Visualize Infrared Images
paths_ir = glob.glob(os.path.join(img_dir, "*.jpg"))
plt.figure(figsize=(20, 20))
for i in range(min(28, len(paths_ir))):
    cur_img = mpimg.imread(paths_ir[i]) 
    ax = plt.subplot(7, 7, i + 1)
    plt.imshow(cur_img.astype("uint8"))
    plt.axis("off")
plt.show()

# Visualize Reference Images
paths_ref = glob.glob("DATASET/insat3d_for_reference_ds/CYCLONE_DATASET/*.jpeg")
plt.figure(figsize=(20, 14))
for i in range(min(4, len(paths_ref))):
    cur_img = mpimg.imread(paths_ref[i]) 
    ax = plt.subplot(2, 2, i + 1)
    plt.imshow(cur_img.astype("uint8"))
    plt.axis("off")
plt.show()

# Image Data Generator with Validation Split
datagen = ImageDataGenerator(rescale=1.0/255.0, validation_split=0.2)

# Training Data
train_data = datagen.flow_from_dataframe(
    dataframe=train,
    directory=img_dir,
    x_col="img_name",
    y_col="label",
    target_size=(512, 512),
    batch_size=16,
    class_mode='raw',
    subset='training',
    shuffle=True,
    seed=42
)

# Validation Data
val_data = datagen.flow_from_dataframe(
    dataframe=train,
    directory=img_dir,
    x_col="img_name",
    y_col="label",
    target_size=(512, 512),
    batch_size=16,
    class_mode='raw',
    subset='validation',
    shuffle=True,
    seed=42
)

# Check Data Batch Shapes
for image_batch, labels_batch in train_data:
    print("Image batch shape:", image_batch.shape)
    print("Labels batch shape:", labels_batch.shape)
    break

# Build Model
def build_model():
    base = Xception(weights="imagenet", include_top=False, input_tensor=Input(shape=(512, 512, 3)))
    base.trainable = False
    flatten = Flatten()(base.output)
    bboxHead = Dense(64, activation="relu")(flatten)
    bboxHead = Dense(32, activation="relu")(bboxHead)
    bboxHead = Dense(1, activation="linear")(bboxHead)
    model = Model(inputs=base.input, outputs=bboxHead)
    return model

model = build_model()
model.summary()

# Compile Model
model.compile(optimizer=tf.keras.optimizers.Adam(learning_rate=0.001), loss='mae', metrics=[tf.keras.metrics.RootMeanSquaredError()])

# Callbacks
save_best = ModelCheckpoint("Model.h5", monitor='val_loss', save_best_only=True, verbose=1)
reduce_lr = ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=5, verbose=1, min_lr=1e-6)
early_stop = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)

# Train Model
history = model.fit(
    train_data,
    validation_data=val_data,
    epochs=30,
    callbacks=[save_best, reduce_lr, early_stop]
)

# Load Best Saved Model
model = tf.keras.models.load_model('Model.h5')

# Evaluate on Validation Data
model.evaluate(val_data)

# Predict on Validation Data
pred = model.predict(val_data, verbose=1).round(2)

# Plot Predictions vs Original
batch_images, batch_labels = next(iter(val_data))
batch_size = batch_images.shape[0]

plt.figure(figsize=(20, 20))
for i in range(batch_size):
    ax = plt.subplot(4, 4, i + 1)
    plt.imshow(batch_images[i])
    plt.title(f"O-{batch_labels[i]:.1f}  P-{pred[i][0]:.1f}")
    plt.axis("off")
plt.show()