<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tropical Cyclone Intensity | Analytics Dashboard</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #34a853;
            --accent-color: #fbbc05;
            --danger-color: #ea4335;
            --dark-color: #202124;
            --light-color: rgba(248, 249, 250, 0.85);
            --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            color: var(--dark-color);
            overflow-x: hidden;
            min-height: 100vh;
            background: url('https://media.giphy.com/media/26BRzozgqiq6qUQxq/giphy.gif') center/cover no-repeat fixed;
            position: relative;
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.4);
            z-index: -1;
        }
        
        .navbar {
            background-color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            backdrop-filter: blur(5px);
        }
        
        .navbar-brand {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            color: var(--primary-color) !important;
            font-size: 1.5rem;
        }
        
        .nav-link {
            font-weight: 500;
            color: var(--dark-color) !important;
            margin: 0 10px;
            transition: var(--transition);
        }
        
        .nav-link:hover, .nav-link.active {
            color: var(--primary-color) !important;
            transform: translateY(-2px);
        }
        
        .dashboard-header {
            text-align: center;
            margin: 60px 0 40px;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .dashboard-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            position: relative;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .dashboard-title:after {
            content: '';
            position: absolute;
            width: 80px;
            height: 4px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 2px;
        }
        
        .dashboard-subtitle {
            max-width: 700px;
            margin: 0 auto;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .graph-row {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            margin: 40px 0;
        }
        
        .graph-card {
            flex: 1;
            min-width: 300px;
            max-width: 400px;
            background: var(--light-color);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            border: none;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .graph-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.4);
        }
        
        .graph-card-header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 15px 20px;
            font-weight: 600;
        }
        
        .graph-card-body {
            padding: 20px;
        }
        
        .graph-image-container {
            width: 100%;
            height: 250px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 6px;
            overflow: hidden;
            transition: var(--transition);
        }
        
        .graph-card:hover .graph-image-container {
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        .graph-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            transition: var(--transition);
        }
        
        .graph-card:hover .graph-image {
            transform: scale(1.03);
        }
        
        .graph-card-footer {
            padding: 15px 20px;
            background-color: rgba(248, 249, 250, 0.7);
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            text-align: right;
        }
        
        .btn-analytics {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 20px;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        .btn-analytics:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            color: white;
        }
        
        @media (max-width: 992px) {
            .graph-card {
                min-width: 280px;
            }
        }
        
        @media (max-width: 768px) {
            .graph-row {
                flex-direction: column;
                align-items: center;
            }
            
            .graph-card {
                width: 100%;
                max-width: 100%;
            }
            
            body {
                background-attachment: scroll;
            }
        }
        
        /* Animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-card {
            animation: fadeIn 0.6s ease forwards;
            opacity: 0;
        }
        
        /* Particle background alternative */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <!-- Animated Background (alternative option) -->
    <!-- <div id="particles-js" class="particles"></div> -->
    
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>Cyclone Analytics
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="userlog.html"><i class="fas fa-home me-1"></i> Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="graph.html"><i class="fas fa-chart-bar me-1"></i> Analytics</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5">
        <div class="dashboard-header">
            <h1 class="dashboard-title">Tropical Cyclone Intensity</h1>
            <p class="dashboard-subtitle">Advanced visualizations and predictive analytics for cyclone intensity patterns</p>
        </div>
        
        <div class="graph-row">
            {% if img %}
            <div class="graph-card animate-card" style="animation-delay: 0.1s">
                <div class="graph-card-header">
                    <i class="fas fa-chart-pie me-2"></i>Distribution Analysis
                </div>
                <div class="graph-card-body">
                    <div class="graph-image-container">
                        <img src="{{img}}" class="graph-image" alt="Cyclone distribution visualization">
                    </div>
                </div>
                <div class="graph-card-footer">
                    <button class="btn btn-analytics btn-sm">View Details</button>
                </div>
            </div>
            {% endif %}
            
            {% for i in range(3) %}
            <div class="graph-card animate-card" style="animation-delay: {{0.2 + i*0.1}}s">
                <div class="graph-card-header">
                    <i class="fas fa-{{ 'wind' if i==0 else 'chart-line' if i==1 else 'map-marked-alt' }} me-2"></i>
                    {{content[i]}}
                </div>
                <div class="graph-card-body">
                    <div class="graph-image-container">
                        <img src="{{images[i]}}" class="graph-image" alt="Cyclone data visualization">
                    </div>
                </div>
                <div class="graph-card-footer">
                    <button class="btn btn-analytics btn-sm">Explore</button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Particles.js (alternative background option) -->
    <!-- <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script> -->
    
    <script>
        // Simple animation trigger
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.animate-card');
            
            cards.forEach((card, index) => {
                // Apply staggered animation delay
                card.style.animationDelay = `${index * 0.1 + 0.1}s`;
                
                // Make sure cards are visible when printed
                card.style.opacity = '1';
            });
            
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });
            
            // Initialize particles.js if using that background
            /* 
            if (typeof particlesJS !== 'undefined') {
                particlesJS.load('particles-js', '{{url_for("static", filename="particles.json")}}', function() {
                    console.log('Particles.js loaded');
                });
            }
            */
        });
    </script>
</body>
</html>