<!DOCTYPE html>
<html lang="en">
<head>
  <title>Tropical Cyclone Intensity</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/he/1.2.0/he.js"></script>

  <style>
    body {
      background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), 
                  url('https://i.gifer.com/7VE.gif') no-repeat center center fixed;
      background-size: cover;
      font-family: 'Arial', sans-serif;
      color: #fff;
      min-height: 100vh;
    }
    
    .container {
      max-width: 600px;
      margin: 50px auto;
      padding: 0 15px;
    }
    
    .form-panel {
      background: rgba(0, 0, 0, 0.7);
      border-radius: 5px;
      padding: 30px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
      margin-bottom: 20px;
      transition: all 0.3s ease;
      border: 1px solid rgba(51, 122, 183, 0.3);
    }
    
    .form-panel:hover {
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.6);
      border-color: rgba(51, 122, 183, 0.6);
    }
    
    .form-title {
      color: #fff;
      text-align: center;
      margin-bottom: 25px;
      font-weight: 600;
      text-transform: uppercase;
      position: relative;
      padding-bottom: 10px;
    }
    
    .form-title:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 2px;
      background: #337ab7;
    }
    
    .form-control {
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: #fff;
      height: 40px;
      margin-bottom: 15px;
    }
    
    .form-control:focus {
      border-color: #337ab7;
      box-shadow: 0 0 0 0.2rem rgba(51, 122, 183, 0.25);
      background-color: rgba(255, 255, 255, 0.15);
      color: #fff;
    }
    
    .btn-submit {
      background-color: #337ab7;
      border: none;
      padding: 10px 20px;
      font-weight: 600;
      width: 100%;
      margin-top: 10px;
      transition: all 0.3s;
    }
    
    .btn-submit:hover {
      background-color: #286090;
      transform: translateY(-2px);
    }
    
    .form-footer {
      text-align: center;
      margin-top: 20px;
      color: #ccc;
    }
    
    .form-footer a {
      color: #337ab7;
      font-weight: 600;
    }
    
    .form-footer a:hover {
      color: #286090;
      text-decoration: none;
    }
    
    .navbar {
      background-color: rgba(0, 0, 0, 0.8);
      border: none;
      border-radius: 0;
      border-bottom: 2px solid #337ab7;
    }
    
    .navbar-brand {
      font-weight: 600;
      color: #fff !important;
    }
    
    .nav li a {
      color: #ddd !important;
      transition: all 0.3s;
    }
    
    .nav li a:hover {
      color: #fff !important;
      background-color: rgba(51, 122, 183, 0.3) !important;
    }
    
    #home, #form1, #form2, #form3, #form4 {
      display: none;
    }
    
    #home.active, #form1.active, #form2.active, #form3.active, #form4.active {
      display: block;
      animation: fadeIn 0.5s ease;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .input-icon {
      position: relative;
    }
    
    .input-icon i {
      position: absolute;
      left: 10px;
      top: 10px;
      color: #ccc;
    }
    
    .input-icon input {
      padding-left: 35px;
    }
    
    .alert-warning {
      background-color: rgba(255, 193, 7, 0.2);
      border-color: rgba(255, 193, 7, 0.3);
      color: #ffc107;
    }
  </style>
</head>

<body>

<nav class="navbar navbar-inverse">
  <div class="container-fluid">
    <div class="navbar-header">
      <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#myNavbar">
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>                        
      </button>
      <a class="navbar-brand" data-value="#form1" onclick="toggleform(event)">
        <i class="fas fa-hurricane"></i> Tropical Cyclone Intensity
      </a>
    </div>
    <div class="collapse navbar-collapse" id="myNavbar">
      <ul class="nav navbar-nav">
        <li><a data-value="#form1" onclick="toggleform(event)">
          <i class="fas fa-home"></i> Home
        </a></li>
      </ul>
      <ul class="nav navbar-nav navbar-right">
        <li><a data-value="#form1" onclick="toggleform(event)">
          <i class="fas fa-user"></i> User
        </a></li>
      </ul>
    </div>
  </div>
</nav>

<div class="container">
  {% if msg %}
  <div class="alert alert-warning alert-dismissible">
    <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
    <strong><i class="fas fa-exclamation-triangle"></i> Warning!</strong> {{msg}}
  </div>
  {% endif %}
  
  <!-- Login Form -->
  <form method="post" action="{{ url_for('userlog')}}" id="form1" class="form-panel active">
    <h2 class="form-title"><i class="fas fa-sign-in-alt"></i> User Login</h2>
    <div class="form-group input-icon">
      <i class="fas fa-user"></i>
      <input type="text" class="form-control" id="username" placeholder="Enter username" name="name" required>
    </div>
    <div class="form-group input-icon">
      <i class="fas fa-lock"></i>
      <input type="password" class="form-control" id="password" placeholder="Enter password" name="password" required>
    </div>
    <button type="submit" class="btn btn-submit">
      <i class="fas fa-sign-in-alt"></i> Submit
    </button>
    <div class="form-footer">
      Click here to <a data-value="#form2" onclick="toggleform(event)">Sign up</a>
    </div>
  </form>

  <!-- Registration Form -->
  <form method="post" action="{{ url_for('userreg')}}" id="form2" class="form-panel">
    <h2 class="form-title"><i class="fas fa-user-plus"></i> User Registration</h2>
    <div class="form-group input-icon">
      <i class="fas fa-user"></i>
      <input type="text" class="form-control" id="username" placeholder="Enter username" name="name" required>
    </div>
    <div class="form-group input-icon">
      <i class="fas fa-envelope"></i>
      <input type="email" class="form-control" id="email" placeholder="Enter email" name="email" required>
    </div>
    <div class="form-group input-icon">
      <i class="fas fa-phone"></i>
      <input type="tel" class="form-control" id="phone" placeholder="Enter mobile number" name="phone" required>
    </div>
    <div class="form-group input-icon">
      <i class="fas fa-lock"></i>
      <input type="password" class="form-control" id="password" placeholder="Enter password" name="password" required>
    </div>
    <button type="submit" class="btn btn-submit">
      <i class="fas fa-user-plus"></i> Submit
    </button>
    <div class="form-footer">
      Click here to <a data-value="#form1" onclick="toggleform(event)">Sign in</a>
    </div>
  </form>
</div>

<script>
  function toggleform(e) {
    var Id = e.target.getAttribute('data-value');
    let Items = ['#form1', '#form2'];
    
    Items.forEach(function(item) {
      if(Id === item) {
        $(item).addClass("active");
      } else {
        $(item).removeClass("active");
      }
    });
  }
</script>

</body>
</html>