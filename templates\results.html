<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyclone Prediction Results | AI Analysis</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #34a853;
            --accent-color: #fbbc05;
            --danger-color: #ea4335;
            --dark-color: #202124;
            --light-color: rgba(248, 249, 250, 0.9);
            --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            color: white;
            min-height: 100vh;
            background: url('https://media.giphy.com/media/l0HU7JI1uZQzqlU7S/giphy.gif') center/cover no-repeat fixed;
            position: relative;
            padding-top: 70px; /* Added for fixed navbar */
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: -1;
        }
        
        /* Navigation Bar Styles */
        .navbar {
            background: rgba(15, 23, 42, 0.9);
            backdrop-filter: blur(8px);
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.2);
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }
        
        .nav-link {
            font-weight: 500;
            padding: 8px 15px;
            margin: 0 5px;
            border-radius: 20px;
            transition: var(--transition);
        }
        
        .nav-link.predict {
            background: linear-gradient(to right, var(--primary-color), #0d6efd);
            color: white !important;
        }
        
        .nav-link.logout {
            background: rgba(255, 255, 255, 0.1);
            color: white !important;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .nav-link.predict:hover {
            background: linear-gradient(to right, #0d6efd, var(--primary-color));
        }
        
        .nav-link.logout:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .results-container {
            max-width: 1200px;
            margin: 40px auto;
            background: rgba(15, 23, 42, 0.85);
            border-radius: 15px;
            padding: 40px;
            box-shadow: var(--card-shadow);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            animation: fadeIn 0.8s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .page-title {
            font-weight: 700;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            padding-bottom: 15px;
        }
        
        .page-title::after {
            content: '';
            position: absolute;
            width: 100px;
            height: 4px;
            background: linear-gradient(to right, var(--primary-color), var(--accent-color));
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 2px;
        }
        
        .result-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 4px solid var(--primary-color);
            transition: var(--transition);
        }
        
        .result-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .result-label {
            font-weight: 600;
            color: var(--accent-color);
            margin-right: 8px;
        }
        
        .result-value {
            font-weight: 500;
        }
        
        .intensity-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--accent-color);
        }
        
        .confidence-meter {
            height: 10px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            margin-top: 10px;
            overflow: hidden;
        }
        
        .confidence-bar {
            height: 100%;
            background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
            border-radius: 5px;
            transition: width 1s ease;
        }
        
        .image-section {
            margin: 40px 0;
        }
        
        .section-title {
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .image-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            overflow: hidden;
            transition: var(--transition);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .image-card:hover {
            transform: scale(1.03);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        
        .image-label {
            padding: 12px;
            background: rgba(0, 0, 0, 0.5);
            text-align: center;
            font-weight: 500;
        }
        
        .image-container {
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
        }
        
        .image-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 5px;
        }
        
        @media (max-width: 768px) {
            .results-container {
                padding: 25px 15px;
                margin: 20px;
            }
            
            .image-grid {
                grid-template-columns: 1fr;
            }
            
            body {
                background-attachment: scroll;
                padding-top: 60px;
            }
            
            .navbar {
                padding: 10px 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>Cyclone Analytics
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a href="/userlog" class="nav-link predict">
                            <i class="fas fa-redo me-1"></i> Predict Another
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="/graph" class="nav-link predict">
                            <i class="fas fa-redo me-1"></i> Graph
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/logout" class="nav-link logout">
                            <i class="fas fa-sign-out-alt me-1"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
        <div class="results-container">
            <h1 class="page-title">Cyclone Prediction Results</h1>
            
            <div class="result-card">
                <div class="row">
                    <div class="col-md-6">
                        <p><span class="result-label">Prediction Method:</span>
                        <span class="result-value">{{ method }}</span></p>
                        
                        <p><span class="result-label">Predicted Class:</span>
                        <span class="result-value">{{ result }}</span></p>
                        
                        {% if method == 'Regression' %}
                        <p><span class="result-label">Predicted Intensity:</span>
                        <span class="intensity-value">{{ intensity }} knots</span></p>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6">
                        <p><span class="result-label">Confidence:</span>
                        <span class="result-value">{{ confidence }}%</span></p>
                        
                        <div class="confidence-meter">
                            <div class="confidence-bar" style="width: {{ confidence }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="image-section">
                <h3 class="section-title">Input Image</h3>
                <div class="image-grid">
                    <div class="image-card">
                        <div class="image-container">
                            <img src="{{ image }}" alt="Input Cyclone Image">
                        </div>
                        <div class="image-label">Original Satellite Image</div>
                    </div>
                </div>
            </div>

            
             {% if method == 'Classification' %}
        <h3>Prediction Probability Graph</h3>
        <img src="{{ graph }}" width="600"><br><br>
    {% endif %}

            
            <div class="image-section">
                <h3 class="section-title">Image Processing Pipeline</h3>
                <div class="image-grid">
                    <div class="image-card">
                        <div class="image-container">
                            <img src="{{ gray }}" alt="Grayscale Image">
                        </div>
                        <div class="image-label">Grayscale Conversion</div>
                    </div>
                    
                    <div class="image-card">
                        <div class="image-container">
                            <img src="{{ edges }}" alt="Edge Detection">
                        </div>
                        <div class="image-label">Edge Detection (Canny)</div>
                    </div>
                    
                    <div class="image-card">
                        <div class="image-container">
                            <img src="{{ thresh }}" alt="Threshold Image">
                        </div>
                        <div class="image-label">Threshold Applied</div>
                    </div>
                    
                    <div class="image-card">
                        <div class="image-container">
                            <img src="{{ sharp }}" alt="Sharpened Image">
                        </div>
                        <div class="image-label">Sharpened Image</div>
                    </div>
                    
                    <div class="image-card">
                        <div class="image-container">
                            <img src="{{ segment }}" alt="Segmented Image">
                        </div>
                        <div class="image-label">Segmented Result</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Animate confidence bar on page load
        document.addEventListener('DOMContentLoaded', function() {
            const confidenceBar = document.querySelector('.confidence-bar');
            // Reset width to 0 then animate to actual value
            confidenceBar.style.width = '0';
            setTimeout(() => {
                confidenceBar.style.width = '{{ confidence }}%';
            }, 300);
        });
        
    </script>
</body>
</html>