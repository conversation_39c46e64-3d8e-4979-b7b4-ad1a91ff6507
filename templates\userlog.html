<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyclone Intensity Prediction | Upload</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #34a853;
            --accent-color: #fbbc05;
            --danger-color: #ea4335;
            --dark-color: #202124;
            --light-color: rgba(248, 249, 250, 0.9);
            --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            color: white;
            min-height: 100vh;
            background: url('https://media.giphy.com/media/l0HU7JI1uZQzqlU7S/giphy.gif') center/cover no-repeat fixed;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: -1;
        }
        
        .upload-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(15, 23, 42, 0.8);
            border-radius: 15px;
            padding: 40px;
            box-shadow: var(--card-shadow);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            animation: fadeInUp 0.8s ease;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .page-title {
            font-weight: 700;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            padding-bottom: 15px;
        }
        
        .page-title::after {
            content: '';
            position: absolute;
            width: 80px;
            height: 4px;
            background: linear-gradient(to right, var(--primary-color), var(--accent-color));
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 2px;
        }
        
        .form-label {
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
        }
        
        .form-control {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 15px;
            border-radius: 8px;
            transition: var(--transition);
        }
        
        .form-control:focus {
            background-color: rgba(255, 255, 255, 0.2);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(26, 115, 232, 0.25);
            color: white;
        }
        
        .form-select {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 15px;
            border-radius: 8px;
            cursor: pointer;
        }
        
        .form-select option {
            background-color: #0f172a;
            color: white;
        }
        
        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(26, 115, 232, 0.25);
        }
        
        .btn-predict {
            background: linear-gradient(45deg, var(--primary-color), #0d6efd);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            letter-spacing: 0.5px;
            text-transform: uppercase;
            transition: var(--transition);
            width: 100%;
            margin-top: 20px;
            box-shadow: 0 4px 15px rgba(26, 115, 232, 0.4);
        }
        
        .btn-predict:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(26, 115, 232, 0.6);
            color: white;
        }
        
        .logout-link {
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
            display: block;
            margin-top: 30px;
            transition: var(--transition);
            text-decoration: none;
        }
        
        .logout-link:hover {
            color: white;
            text-decoration: underline;
        }
        
        .file-upload-wrapper {
            position: relative;
            margin-bottom: 20px;
        }
        
        .file-upload-label {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 40px 20px;
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            transition: var(--transition);
            cursor: pointer;
        }
        
        .file-upload-label:hover {
            border-color: var(--primary-color);
            background-color: rgba(26, 115, 232, 0.1);
        }
        
        .file-upload-icon {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .file-upload-text {
            text-align: center;
        }
        
        .file-upload-text span {
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .file-upload-input {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .upload-container {
                padding: 30px 20px;
                margin: 20px;
            }
            
            body {
                background-attachment: scroll;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="upload-container">
            <h1 class="page-title">Cyclone Intensity Prediction</h1>
            
            <form action="/predict" method="POST" enctype="multipart/form-data" id="predictionForm">
                <div class="mb-4">
                    <div class="file-upload-wrapper">
                        <label class="file-upload-label">
                            <i class="fas fa-cloud-upload-alt file-upload-icon"></i>
                            <div class="file-upload-text">
                                <span>Click to upload</span> or drag and drop<br>
                                Satellite image of cyclone (JPG, PNG)
                            </div>
                            <input type="file" name="image" class="file-upload-input" required>
                        </label>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="form-label">Select Prediction Method:</label>
                    <select name="method" class="form-select" required>
                        <option value="classification">Classification (Intensity Category)</option>
                        <option value="regression">Regression (Wind Speed Prediction)</option>
                    </select>
                </div>
                
                <button type="submit" class="btn btn-predict">
                    <i class="fas fa-bolt me-2"></i> Predict Now
                </button>
            </form>
            
            <a href="/logout" class="logout-link">
                <i class="fas fa-sign-out-alt me-2"></i> Logout
            </a>
        </div>
    </div>

    <!-- Flask flash popup (Bootstrap alert) -->
    {% with messages = get_flashed_messages() %}
      {% if messages %}
        <script>
          alert("{{ messages[0] }}");
        </script>
      {% endif %}
    {% endwith %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Allowed dataset directories for validation
        const allowedDirectories = [
            'DATASET',
            'Train/T1(0-45)',
            'Train/T2(45-55)',
            'Train/T3(55-83)',
            'Train/T4(83-120)',
            'Train/T5(120-167)',
            'Train/T6(167-213)',
            'Train/T7(213-260)',
            'Train/T8(260-315)',
            'Test/T1(0-45)',
            'Test/T2(45-55)',
            'Test/T3(55-83)',
            'Test/T4(83-120)',
            'Test/T5(120-167)',
            'Test/T6(167-213)',
            'Test/T7(213-260)',
            'Test/T8(260-315)'
        ];

        // Function to validate if filename is from allowed directories
        function validateImageSource(fileName) {
            // This is a basic client-side check based on filename patterns
            // The server-side validation is more thorough
            return true; // Allow client-side, server will do the real validation
        }

        // File upload name display
        document.querySelector('.file-upload-input').addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name || 'No file selected';
            document.querySelector('.file-upload-text').innerHTML =
                `<span>${fileName}</span>`;
        });
        
        // Drag and drop functionality
        const fileUploadLabel = document.querySelector('.file-upload-label');
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            fileUploadLabel.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        ['dragenter', 'dragover'].forEach(eventName => {
            fileUploadLabel.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            fileUploadLabel.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            fileUploadLabel.style.borderColor = '#1a73e8';
            fileUploadLabel.style.backgroundColor = 'rgba(26, 115, 232, 0.1)';
        }
        
        function unhighlight() {
            fileUploadLabel.style.borderColor = 'rgba(255, 255, 255, 0.3)';
            fileUploadLabel.style.backgroundColor = 'transparent';
        }
        
        fileUploadLabel.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            document.querySelector('.file-upload-input').files = files;
            document.querySelector('.file-upload-text').innerHTML =
                `<span>${files[0].name}</span>`;
        }

        // Form submission validation
        document.getElementById('predictionForm').addEventListener('submit', function(e) {
            const fileInput = document.querySelector('.file-upload-input');
            const fileName = fileInput.files[0]?.name;

            if (!fileName) {
                e.preventDefault();
                alert('Please select an image file before submitting.');
                return false;
            }

            // Note: The main validation happens on the server side
            // This is just a basic client-side check for user experience
        });
    </script>
</body>
</html>
